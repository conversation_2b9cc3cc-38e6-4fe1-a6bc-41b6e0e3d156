package com.aa.ccrecon.detailacctngfeedhandler.entity;

import com.aa.ccrecon.domain.subledger.TransactionType;
import com.aa.itfacs.pmt.mask.annotation.Mask;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "ara_accounting_details", schema = "rec_account")
public class AraAccountingDetails {

    @Id
    @Column(name = "ara_accounting_details_id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer araAccountingDetailsId;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_details_id")
    private Integer fileDetailsId;

    @Column(name = "file_record_number")
    private Integer fileRecordNumber;

    @Mask
    @Column(name = "document_number")
    private String documentNumber;

    @Column(name = "issuance_date", columnDefinition = "DATE")
    private LocalDate issuanceDate;

    @Column(name = "post_to_ledger")
    private String postToLedger;

    @Column(name = "accounted_amount")
    private BigDecimal accountedAmount;

    @Column(name = "original_amount")
    private BigDecimal originalAmount;

    @Column(name = "original_currency")
    private String originalCurrency;

    @Column(name = "accounting_date", columnDefinition = "DATE")
    private LocalDate accountingDate;

    @Column(name = "sign")
    private String sign;

    @Column(name = "degraded")
    private String degraded;

    @Column(name = "transaction")
    private String transaction;

    @Enumerated(EnumType.STRING)
    private TransactionType.TYPES transactionType;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_timestamp", insertable=false, updatable=false)
    private Date createdTimestamp;

}